<?php

namespace Packages\Uranus\app\Http\Controllers;

use App\Models\School;
use Aws\Exception\AwsException;
use Aws\Translate\TranslateClient;
use Illuminate\Http\Request;

class SchoolTranslationsController extends Controller
{
    protected $translationClient;
    protected $sourceLang = 'el';
    protected $targetLang = 'en';

    public function __construct()
    {
        $this->translationClient = new TranslateClient([
            'version' => 'latest',
            'region'  => config('aws.region'),
            'credentials' => [
                'key'    => config('aws.credentials.key'),
                'secret' => config('aws.credentials.secret'),
            ],
        ]);
    }

    /**
     * Suggest translation for school name or description
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function suggest(Request $request)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_theatres');

        $school = School::findOrFail($request->input('school_id'));
        $field = $request->input('field', 'name');

        if (empty($school->$field)) {
            return response()->json([
                'success' => false,
                'message' => 'No content to translate'
            ]);
        }

        // Strip HTML tags for translation
        $textToTranslate = strip_tags($school->$field);

        if (empty($textToTranslate)) {
            return response()->json([
                'success' => false,
                'message' => 'No content to translate after removing HTML tags'
            ]);
        }

        // AWS imposes a request limit for 5000 bytes of translatable text
        if (mb_strlen($textToTranslate) >= 2000) {
            return response()->json([
                'success' => false,
                'message' => 'Content too long for translation'
            ]);
        }

        try {
            $result = $this->translationClient->translateText([
                'SourceLanguageCode' => $this->sourceLang,
                'TargetLanguageCode' => $this->targetLang,
                'Text' => $textToTranslate,
            ]);

            return response()->json([
                'success' => true,
                'translation' => $result->get('TranslatedText')
            ]);

        } catch (AwsException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Translation service error: ' . $e->getMessage()
            ]);
        }
    }
}
