<div class="form-group row">
    <div class="col-sm-12">
        <label for="name" class="control-label">Όνομα σχολής:*</label>
        <input type="text" class="form-control" name="name" id="name" value="{!! $school->name !!}" readonly="readonly">
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12">
        <a style="cursor:pointer" data-url="{!! route('uranus.schools.translation.suggest') !!}" href="" id="translate_name">Πρότεινε μετάφρασιν</a>
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12">
        <label for="name_en" class="control-label">Όνομα σχολής [Αγγλικά]:*</label>
        <input type="text" class="form-control" name="name_en" id="name_en" value="{{ old('name_en',($school->hasTranslation('en') ? $school->translate('en')->name : "")) }}">
    </div>
</div>

<div class="form-group row">
    <div class="col-sm-12">
        <label for="description" class="control-label">Περιγραφή:</label>
        <textarea class="form-control" name="description" id="description" readonly="readonly">{!! $school->description !!}</textarea>
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12">
        <a style="cursor:pointer" data-url="{!! route('uranus.schools.translation.suggest') !!}" href="" id="translate_description">Πρότεινε μετάφρασιν</a>
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12">
        <label for="description_en" class="control-label">Περιγραφή [Αγγλικά]:</label>
        <textarea class="form-control wysiwyg" name="description_en" id="description_en">{{ old('description_en',($school->hasTranslation('en') ? $school->translate('en')->description : "")) }}</textarea>
    </div>
</div>

<div class="form-group row">
    <div class="col-sm-12 col-md-6">
        <button type="submit" class="btn btn-block btn-success">{{ $submitButtonText }}</button>
    </div>
</div>

@section('footer')
    @parent
    <script type="text/javascript" src="{{ asset("js/admin/tinymce/tinymce.min.js") }}"></script>
    <script type="text/javascript">
        tinymce.init({
            selector: ".wysiwyg",
            plugins: [
                'advlist autolink lists link charmap preview anchor',
                'paste code fullscreen hr'
            ],
            entity_encoding: "raw",
            paste_auto_cleanup_on_paste: true,
            paste_preprocess: function (pl, o)
            {
                // Content string containing the HTML from the clipboard
                o.content = o.content;
            },
            paste_postprocess: function (pl, o)
            {
                // Content DOM node containing the DOM structure of the clipboard
                o.node.innerHTML = o.node.innerHTML;
            }
        });
    </script>
@stop
