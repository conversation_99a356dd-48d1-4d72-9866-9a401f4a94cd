$(document).ready(function() {

    // Handle translation suggestion for name
    $('#translate_name').click(function(e) {
        e.preventDefault();

        var url = $(this).data('url');
        var schoolId = $('input[name="school_id"]').val() || window.location.pathname.split('/').slice(-2, -1)[0];

        // Show loading state
        $(this).html('<i class="fa fa-spinner fa-spin"></i> Μετάφραση...');
        $(this).prop('disabled', true);

        $.ajax({
            url: url,
            type: 'POST',
            data: {
                school_id: schoolId,
                field: 'name',
                _token: $('input[name="_token"]').val()
            },
            success: function(response) {
                if (response.success) {
                    $('#name_en').val(response.translation);
                    $('#translate_name').html('Πρότεινε μετάφρασιν');
                    $('#translate_name').prop('disabled', false);

                    // Show success message
                    if ($('.alert-success').length === 0) {
                        $('#name_en').after('<div class="alert alert-success alert-dismissible mt-2" role="alert">' +
                            '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
                            '<span aria-hidden="true">&times;</span></button>' +
                            '<i class="icon md-check" aria-hidden="true"></i> Η μετάφραση προτάθηκε επιτυχώς!' +
                            '</div>');
                    }
                } else {
                    alert('Σφάλμα μετάφρασης: ' + response.message);
                    $('#translate_name').html('Πρότεινε μετάφρασιν');
                    $('#translate_name').prop('disabled', false);
                }
            },
            error: function() {
                alert('Σφάλμα σύνδεσης με την υπηρεσία μετάφρασης');
                $('#translate_name').html('Πρότεινε μετάφρασιν');
                $('#translate_name').prop('disabled', false);
            }
        });
    });

    // Handle translation suggestion for description
    $('#translate_description').click(function(e) {
        e.preventDefault();

        var url = $(this).data('url');
        var schoolId = $('input[name="school_id"]').val() || window.location.pathname.split('/').slice(-2, -1)[0];

        // Show loading state
        $(this).html('<i class="fa fa-spinner fa-spin"></i> Μετάφραση...');
        $(this).prop('disabled', true);

        $.ajax({
            url: url,
            type: 'POST',
            data: {
                school_id: schoolId,
                field: 'description',
                _token: $('input[name="_token"]').val()
            },
            success: function(response) {
                if (response.success) {
                    // Set the content in TinyMCE editor
                    if (tinymce.get('description_en')) {
                        tinymce.get('description_en').setContent(response.translation);
                    } else {
                        $('#description_en').val(response.translation);
                    }
                    $('#translate_description').html('Πρότεινε μετάφρασιν');
                    $('#translate_description').prop('disabled', false);

                    // Show success message
                    if ($('.alert-success').length === 0) {
                        $('#description_en').after('<div class="alert alert-success alert-dismissible mt-2" role="alert">' +
                            '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
                            '<span aria-hidden="true">&times;</span></button>' +
                            '<i class="icon md-check" aria-hidden="true"></i> Η μετάφραση προτάθηκε επιτυχώς!' +
                            '</div>');
                    }
                } else {
                    alert('Σφάλμα μετάφρασης: ' + response.message);
                    $('#translate_description').html('Πρότεινε μετάφρασιν');
                    $('#translate_description').prop('disabled', false);
                }
            },
            error: function() {
                alert('Σφάλμα σύνδεσης με την υπηρεσία μετάφρασης');
                $('#translate_description').html('Πρότεινε μετάφρασιν');
                $('#translate_description').prop('disabled', false);
            }
        });
    });
});
