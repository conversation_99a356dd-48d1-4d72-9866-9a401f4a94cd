$(document).ready(function() {

    // Handle title translation
    $('#translate_title').click(function(e) {
        e.preventDefault();

        var url = $(this).data('url');
        var movieId = window.location.pathname.split('/').slice(-2, -1)[0];

        $.ajax({
            url: url,
            type: 'POST',
            data: {
                movie_id: movieId,
                field: 'title',
                _token: $('input[name="_token"]').val()
            },
            beforeSend: function() {
                $('#translate_title').text('Μεταφράζεται...');
            },
            success: function(response) {
                if (response.success) {
                    $('#title_en').val(response.translation);
                    $('#translate_title').text('Πρότεινε μετάφρασιν');

                    // Show success message at the top of the page content
                    $('.alert-success').remove(); // Remove any existing success messages
                    $('.page-content').prepend('<div class="alert alert-success alert-dismissible" role="alert">' +
                        '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
                        '<span aria-hidden="true">&times;</span></button>' +
                        '<strong>Επιτυχία!</strong> Η μετάφραση του τίτλου προτάθηκε επιτυχώς!' +
                        '</div>');

                    console.log('Success message added for title translation');
                } else {
                    alert('Σφάλμα: ' + response.message);
                    $('#translate_title').text('Πρότεινε μετάφρασιν');
                }
            },
            error: function() {
                alert('Σφάλμα κατά τη μετάφραση');
                $('#translate_title').text('Πρότεινε μετάφρασιν');
            }
        });
    });

    // Handle synopsis translation
    $('#translate_synopsis').click(function(e) {
        e.preventDefault();

        var url = $(this).data('url');
        var movieId = window.location.pathname.split('/').slice(-2, -1)[0];

        $.ajax({
            url: url,
            type: 'POST',
            data: {
                movie_id: movieId,
                field: 'synopsis',
                _token: $('input[name="_token"]').val()
            },
            beforeSend: function() {
                $('#translate_synopsis').text('Μεταφράζεται...');
            },
            success: function(response) {
                if (response.success) {
                    // Set the content in TinyMCE editor if available
                    if (tinymce.get('synopsis_en')) {
                        tinymce.get('synopsis_en').setContent(response.translation);
                    } else {
                        $('#synopsis_en').val(response.translation);
                    }
                    $('#translate_synopsis').text('Πρότεινε μετάφρασιν');

                    // Show success message at the top of the page content
                    $('.alert-success').remove(); // Remove any existing success messages
                    $('.page-content').prepend('<div class="alert alert-success alert-dismissible" role="alert">' +
                        '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
                        '<span aria-hidden="true">&times;</span></button>' +
                        '<strong>Επιτυχία!</strong> Η μετάφραση της περίληψης προτάθηκε επιτυχώς!' +
                        '</div>');

                    console.log('Success message added for synopsis translation');
                } else {
                    alert('Σφάλμα: ' + response.message);
                    $('#translate_synopsis').text('Πρότεινε μετάφρασιν');
                }
            },
            error: function() {
                alert('Σφάλμα κατά τη μετάφραση');
                $('#translate_synopsis').text('Πρότεινε μετάφρασιν');
            }
        });
    });

});
