<?php

namespace App\Console;

use App\Console\Commands\AggregatePersonEvents;
use App\Console\Commands\AggregatePlayEvents;
use App\Console\Commands\DeleteCrawlerVisits;
use App\Console\Commands\ProcessEmailTracking;
use App\Console\Commands\QueueStreamingRecommendationReminder;
use App\Console\Commands\UpdatePersonHistoryRankings;
use App\Console\Commands\UpdatePersonRankings;
use App\Console\Commands\UpdateSinglePlayRating;
use App\Console\Commands\UpdatePlayRatings;
use App\Console\Commands\UpdatePersonRatings;
use App\Console\Commands\UpdatePersonTranslations;
use App\Console\Commands\UpdatePlayTranslations;
use App\Console\Commands\UpdateTvShowTranslations;
use App\Console\Commands\FindMissingTvShowTranslations;
use App\Console\Commands\UpdateMovieTranslations;
use App\Console\Commands\FindMissingMovieTranslations;
use App\Console\Commands\CalculateReferenceNumbers;
use App\Console\Commands\ElasticsearchIndexEntities;
use App\Console\Commands\QueueRecommendationReminder;
use App\Console\Commands\sanitizeImages;
use App\Console\Commands\QueueWatchlistReminder;
use App\Console\Commands\AggregatePersonVisits;
use App\Console\Commands\AggregatePlayVisits;
use App\Console\Commands\AggregateMovieVisits;
use App\Console\Commands\AggregateTvShowVisits;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        DeleteCrawlerVisits::class,
        ElasticsearchIndexEntities::class,
        sanitizeImages::class,
    	QueueWatchlistReminder::class,
    	QueueRecommendationReminder::class,
    	QueueStreamingRecommendationReminder::class,
    	CalculateReferenceNumbers::class,
    	UpdatePlayRatings::class,
    	UpdateSinglePlayRating::class,
    	UpdatePersonRatings::class,
    	UpdatePersonTranslations::class,
    	UpdatePlayTranslations::class,
    	UpdateTvShowTranslations::class,
    	FindMissingTvShowTranslations::class,
    	UpdateMovieTranslations::class,
    	FindMissingMovieTranslations::class,
    	\App\Console\Commands\UpdateSchoolTranslations::class,
    	\App\Console\Commands\FindMissingSchoolTranslations::class,
    	AggregatePersonVisits::class,
    	AggregatePlayVisits::class,
    	AggregateMovieVisits::class,
    	AggregateTvShowVisits::class,
    	AggregatePersonEvents::class,
    	AggregatePlayEvents::class,
    	ProcessEmailTracking::class,
        UpdatePersonRankings::class,
        UpdatePersonHistoryRankings::class,
    ];

    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // calculate reference numbers
    	$schedule->command('reference-numbers:calculate')->twiceDaily(0, 12);

    	// send watchlist reminders
    	$schedule->command('watchlist:sendReminders')->dailyAt('09:04');

    	// send recommendations based on followings
        // every Wednesday and Sunday at 10:14
    	$schedule->command('recommendation:sendReminders')
            ->cron('14 10 * * 3,7');

    	// send streaming recommendations based on followings
        // every Wednesday and Sunday at 17:14
//    	$schedule->command('streamingRecommendation:sendReminders')
//            ->cron('14 17 * * 3,7');

    	// aggregate person visits (calculate weekly and daily counts)
        // 200 * 60 * 12 = 144.000
    	$schedule->command('person-visits:aggregate')
            ->everyMinute()
            ->between('01:00', '13:00');
        // aggregate play visits (calculate weekly and daily counts)
        // 200 * 12 * 12 = 28.800
    	$schedule->command('play-visits:aggregate')
            ->everyFiveMinutes()
            ->between('01:00', '13:00');
        // aggregate movie visits (calculate weekly and daily counts and overall counter)
        // 200 * 2 * 12 = 4.800
        $schedule->command('movie-visits:aggregate')
            ->everyThirtyMinutes()
            ->between('01:00', '13:00');
        // aggregate tv show visits (calculate weekly and daily counts and overall counter)
        // 200 * 2 * 12 = 4.800
        $schedule->command('tvShow-visits:aggregate')
            ->everyThirtyMinutes()
            ->between('01:00', '13:00');

    	// aggregate events
        // 100 * 6 * 5 = 3000
    	$schedule->command('person-events:aggregate')
            ->everyTenMinutes()
            ->between('02:00', '07:00');
    	$schedule->command('play-events:aggregate')
            ->everyTenMinutes()
            ->between('02:00', '07:00');

        // update internal ratings fer peeps
        // we want to update all person ratings every sunday so as to be ready for ranking calculation on monday
        // note: along the updating the person ratings we also migrate user data (in the same command)
        // 100 * 60 / 3 * 48 = 96.000
        $schedule->command('person-ratings:update')
            ->weekends()
            ->everyThreeMinutes();
        // update the rankings fer peeps every monday
        // it only needs one run, but we run it 12 times to be sure :) (and it does not overwrite the existing rows)
        $schedule->command('person-rankings:update')
            ->weekly()
            ->mondays()
            ->everyFiveMinutes()
            ->between('00:00', '01:00');
        // update internal ratings fer plays
        // 50 * 10 = 500
        $schedule->command('play-ratings:update')
            ->hourlyAt(24)
            ->between('01:00', '11:00');

        // update person translations
        // 40 * 96 = 3840
//        $schedule->command('person-translations:update')
//            ->everyFifteenMinutes();

        // update play translations
        $schedule->command('play-translations:update')
            ->everyThreeHours();

        // update movie translations
        $schedule->command('movie-translations:update')
            ->everySixHours();

        // update tvshow translations
        $schedule->command('tvshow-translations:update')
            ->everySixHours();

        // Process email tracking
        $schedule->command('email-tracking:process')
            ->everyFiveMinutes();

        // delete crawler visits
        // 10k per run
        $schedule->command('crawler-visits:delete')
            ->everyFifteenMinutes()
//            ->hourlyAt(44)
            ->between('00:00', '07:00');

        // Generate sitemap
        $schedule->command('sitemap:generate')
            ->weeklyOn(1, '3:00')
            ->weeklyOn(4, '3:00');

        // Clear cache daily
        $schedule->command('cache:clear')->dailyAt('2:30');

        // Process queue
        if (stripos((string) shell_exec('ps xf | grep \'[q]ueue:work\''), 'artisan queue:work') === false) {
            $schedule->command('queue:work --sleep=2 --tries=3 --timeout=5 --stop-when-empty')->everyTwoMinutes();
        }
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
